@import "tailwindcss";
@import "tw-animate-css";

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --destructive-foreground: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --radius: 0.625rem;
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.145 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.145 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.985 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.396 0.141 25.723);
  --destructive-foreground: oklch(0.637 0.237 25.331);
  --border: oklch(0.269 0 0);
  --input: oklch(0.269 0 0);
  --ring: oklch(0.439 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(0.269 0 0);
  --sidebar-ring: oklch(0.439 0 0);
}

@theme inline {
  --font-sans: var(--font-inter);

  /* Modern Design System - Inspired by Vercel/Notion/Figma */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-white: #ffffff;
  --color-black: #000000;

  /* Brand Colors */
  --color-rose-50: #fdf2f8;
  --color-rose-100: #fce7f3;
  --color-rose-500: #f43f5e;
  --color-rose-600: #e11d48;
  --color-rose-700: #be123c;
  
  --color-purple-50: #faf5ff;
  --color-purple-100: #f3e8ff;
  --color-purple-500: #a855f7;
  --color-purple-600: #9333ea;
  --color-purple-700: #7c3aed;
  
  --color-green-50: #f0fdf4;
  --color-green-100: #dcfce7;
  --color-green-500: #22c55e;
  --color-green-600: #16a34a;
  --color-green-700: #15803d;

  /* Semantic Colors */
  --color-success: #10b981;
  --color-warning: #f59e0b;
  --color-error: #ef4444;

  /* Modern Spacing Scale - 8px base */
  --space-0: 0px;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 20px;
  --space-6: 24px;
  --space-8: 32px;
  --space-10: 40px;
  --space-12: 48px;
  --space-16: 64px;
  --space-20: 80px;
  --space-24: 96px;
  --space-32: 128px;
  --space-40: 160px;
  --space-48: 192px;
  --space-64: 256px;

  /* Modern Typography Scale */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */
  --text-7xl: 4.5rem;    /* 72px */
  --text-8xl: 6rem;      /* 96px */
  --text-9xl: 8rem;      /* 128px */

  /* Line Heights */
  --leading-none: 1;
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Shadcn color mappings */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "kern" 1, "liga" 1, "calt" 1;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: transparent;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--color-neutral-400);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--color-neutral-800);
  }

  /* Focus management for accessibility */
  .focus-visible {
    @apply outline-2 outline-offset-2 outline-neutral-900;
  }
}

@layer utilities {
  /* Modern Typography Utilities */
  .text-display-xl {
    font-size: var(--text-8xl);
    font-weight: 800;
    line-height: var(--leading-none);
    letter-spacing: -0.025em;
  }

  .text-display-lg {
    font-size: var(--text-7xl);
    font-weight: 700;
    line-height: var(--leading-tight);
    letter-spacing: -0.02em;
  }

  .text-display-md {
    font-size: var(--text-6xl);
    font-weight: 600;
    line-height: var(--leading-tight);
    letter-spacing: -0.015em;
  }

  .text-heading-xl {
    font-size: var(--text-5xl);
    font-weight: 600;
    line-height: var(--leading-tight);
    letter-spacing: -0.01em;
  }

  .text-heading-lg {
    font-size: var(--text-4xl);
    font-weight: 600;
    line-height: var(--leading-snug);
  }

  .text-heading-md {
    font-size: var(--text-3xl);
    font-weight: 600;
    line-height: var(--leading-snug);
  }

  .text-body-lg {
    font-size: var(--text-lg);
    font-weight: 400;
    line-height: var(--leading-relaxed);
  }

  .text-body {
    font-size: var(--text-base);
    font-weight: 400;
    line-height: var(--leading-normal);
  }

  .text-body-sm {
    font-size: var(--text-sm);
    font-weight: 400;
    line-height: var(--leading-normal);
  }

  .text-label-lg {
    font-size: var(--text-sm);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }

  .text-label {
    font-size: var(--text-xs);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.1em;
  }

  /* Modern Layout Utilities */
  .container-custom {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .container-custom {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding: 0 2rem;
    }
  }

  @media (min-width: 1280px) {
    .container-custom {
      padding: 0 var(--space-6);
    }
  }

  .container-narrow {
    max-width: 768px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .container-narrow {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-narrow {
      padding: 0 var(--space-6);
    }
  }

  .container-wide {
    max-width: 1440px;
    margin: 0 auto;
    padding: 0 1rem;
  }

  @media (min-width: 640px) {
    .container-wide {
      padding: 0 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-wide {
      padding: 0 2rem;
    }
  }

  @media (min-width: 1440px) {
    .container-wide {
      padding: 0 var(--space-6);
    }
  }

  /* Modern Glass Effects */
  .glass-panel {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  }

  .glass-panel-dark {
    backdrop-filter: blur(20px) saturate(180%);
    background: rgba(0, 0, 0, 0.6);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  /* Modern Card Styles */
  .card-modern {
    background: white;
    border-radius: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
    transition: all 0.2s ease;
  }

  .card-modern:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }

  .card-elevated {
    background: white;
    border-radius: 20px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .card-elevated:hover {
    box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1), 0 10px 10px rgba(0, 0, 0, 0.04);
    transform: translateY(-4px);
  }

  /* Modern Button Styles */
  .btn-primary {
    background: var(--color-rose-600);
    color: white;
    padding: var(--space-3) var(--space-6);
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
  }

  .btn-primary:hover {
    background: var(--color-rose-700);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(225, 29, 72, 0.4);
  }

  .btn-secondary {
    background: var(--color-neutral-100);
    color: var(--color-neutral-900);
    padding: var(--space-3) var(--space-6);
    border-radius: 12px;
    font-weight: 500;
    transition: all 0.2s ease;
    border: 1px solid var(--color-neutral-200);
    cursor: pointer;
  }

  .btn-secondary:hover {
    background: var(--color-neutral-200);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  /* Performance optimizations */
  .will-change-transform {
    will-change: transform;
  }

  .will-change-opacity {
    will-change: opacity;
  }

  .will-change-scroll {
    will-change: scroll-position;
  }

  /* Mobile-specific utilities */
  .mobile-safe-area {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Touch targets for mobile */
  .touch-target {
    min-height: 44px;
    min-width: 44px;
  }

  /* Touch manipulation for better mobile performance */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Prevent mobile scroll bounce on menu overlay */
  .mobile-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }

  /* Better mobile button styles */
  .mobile-button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }

  /* Mobile navigation specific styles */
  @media (max-width: 767px) {
    .mobile-nav-backdrop {
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
    }
    
    .mobile-nav-panel {
      box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 0 1px rgba(0, 0, 0, 0.05);
    }
  }

  /* Responsive text sizes */
  .text-responsive-xs {
    font-size: 0.75rem;
  }

  @media (min-width: 640px) {
    .text-responsive-xs {
      font-size: 0.875rem;
    }
  }

  .text-responsive-sm {
    font-size: 0.875rem;
  }

  @media (min-width: 640px) {
    .text-responsive-sm {
      font-size: 1rem;
    }
  }

  .text-responsive-base {
    font-size: 1rem;
  }

  @media (min-width: 640px) {
    .text-responsive-base {
      font-size: 1.125rem;
    }
  }

  .text-responsive-lg {
    font-size: 1.125rem;
  }

  @media (min-width: 640px) {
    .text-responsive-lg {
      font-size: 1.25rem;
    }
  }

  @media (min-width: 1024px) {
    .text-responsive-lg {
      font-size: 1.5rem;
    }
  }

  /* Responsive spacing */
  .space-responsive-x {
    margin-left: 0.5rem;
    margin-right: 0.5rem;
  }

  @media (min-width: 640px) {
    .space-responsive-x {
      margin-left: 1rem;
      margin-right: 1rem;
    }
  }

  @media (min-width: 1024px) {
    .space-responsive-x {
      margin-left: 1.5rem;
      margin-right: 1.5rem;
    }
  }

  .space-responsive-y {
    margin-top: 1rem;
    margin-bottom: 1rem;
  }

  @media (min-width: 640px) {
    .space-responsive-y {
      margin-top: 1.5rem;
      margin-bottom: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .space-responsive-y {
      margin-top: 2rem;
      margin-bottom: 2rem;
    }
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .motion-safe\:animate-none {
    animation: none !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-panel {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid rgba(0, 0, 0, 0.8);
  }
}
