"use client"

import { motion } from "framer-motion"

export function AboutSection() {
  return (
    <section id="about" className="py-20 bg-gradient-to-br from-gray-50 via-white to-gray-100 relative overflow-hidden">
      {/* Elegant background elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 -left-20 w-40 h-40 bg-black/3 rounded-full blur-3xl" />
        <div className="absolute bottom-20 -right-20 w-60 h-60 bg-black/2 rounded-full blur-3xl" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-black/2 rounded-full blur-3xl" />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-5xl md:text-6xl font-bold text-black mb-4 tracking-tight">
            Our Impact
          </h2>
          <div className="w-20 h-1 bg-black mx-auto rounded-full"></div>
        </motion.div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 max-w-4xl mx-auto">
          <motion.div
            className="group text-center"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            viewport={{ once: true }}
            whileHover={{ y: -8 }}
          >
            <div className="bg-white/70 backdrop-blur-sm border border-black/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
              <div className="text-7xl font-black text-black mb-4 leading-none">50+</div>
              <div className="text-black/80 text-lg font-medium tracking-wide">Projects Delivered</div>
              <div className="mt-4 w-12 h-0.5 bg-black mx-auto opacity-30 group-hover:opacity-60 transition-opacity duration-300"></div>
            </div>
          </motion.div>

          <motion.div
            className="group text-center"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            viewport={{ once: true }}
            whileHover={{ y: -8 }}
          >
            <div className="bg-white/70 backdrop-blur-sm border border-black/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
              <div className="text-7xl font-black text-black mb-4 leading-none">25+</div>
              <div className="text-black/80 text-lg font-medium tracking-wide">Happy Clients</div>
              <div className="mt-4 w-12 h-0.5 bg-black mx-auto opacity-30 group-hover:opacity-60 transition-opacity duration-300"></div>
            </div>
          </motion.div>

          <motion.div
            className="group text-center"
            initial={{ opacity: 0, y: 40 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            viewport={{ once: true }}
            whileHover={{ y: -8 }}
          >
            <div className="bg-white/70 backdrop-blur-sm border border-black/10 rounded-2xl p-8 shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:scale-105">
              <div className="text-7xl font-black text-black mb-4 leading-none">5+</div>
              <div className="text-black/80 text-lg font-medium tracking-wide">Years Experience</div>
              <div className="mt-4 w-12 h-0.5 bg-black mx-auto opacity-30 group-hover:opacity-60 transition-opacity duration-300"></div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}